<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الأدمن - نظام إدارة المتاجر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            /* الألوان الأساسية */
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-200: #bae6fd;
            --primary-300: #7dd3fc;
            --primary-400: #38bdf8;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-800: #075985;
            --primary-900: #0c4a6e;

            /* ألوان النص */
            --text-primary: #0c4a6e;
            --text-secondary: #0369a1;
            --text-muted: #64748b;
            --text-white: #ffffff;

            /* ألوان الخلفية */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-card: rgba(255, 255, 255, 0.95);
            --white: #ffffff;

            /* الحدود والظلال */
            --border-light: rgba(14, 165, 233, 0.2);
            --border-medium: rgba(14, 165, 233, 0.3);
            --shadow-sm: 0 4px 15px rgba(14, 165, 233, 0.1);
            --shadow-md: 0 8px 25px rgba(14, 165, 233, 0.15);
            --shadow-lg: 0 20px 40px rgba(14, 165, 233, 0.2);
            --shadow-xl: 0 25px 50px rgba(14, 165, 233, 0.25);

            /* المسافات */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            --spacing-3xl: 4rem;

            /* الحواف المدورة */
            --border-radius-sm: 6px;
            --border-radius-md: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;

            /* الانتقالات */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.4s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #f0f9ff 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            text-align: right;
            min-height: 100vh;
        }

        /* شريط التنقل العلوي */
        .admin-navbar {
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            padding: 1rem 0;
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            color: var(--text-white) !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-decoration: none;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius-md);
            transition: all var(--transition-normal);
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-white) !important;
        }

        .navbar-nav .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-white) !important;
        }

        /* المحتوى الرئيسي */
        .main-content {
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }
        }

        /* تأثيرات الحركة */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسينات Bootstrap */
        .btn {
            border-radius: var(--border-radius-md);
            font-weight: 500;
            transition: all var(--transition-normal);
        }

        .card {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
        }

        .form-control {
            border-radius: var(--border-radius-md);
            border: 2px solid var(--border-light);
            transition: all var(--transition-normal);
        }

        .form-control:focus {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25);
        }

        /* تخصيص الجداول */
        .table {
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            text-align : center;
            vertical-align: middle;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: var(--text-white);
            border: none;
            font-weight: 600;
        }

        /* تخصيص التنبيهات */
        .alert {
            border: none;
            border-radius: var(--border-radius-lg);
            font-weight: 500;
        }

        /* تخصيص الشارات */
        .badge {
            border-radius: var(--border-radius-md);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="admin-navbar">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <a href="/admin/dashboard" class="navbar-brand">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة تحكم الأدمن
                </a>
                
                <ul class="navbar-nav d-flex flex-row">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-home"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/auth/profile">
                            <i class="fas fa-user"></i>
                            البروفايل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/notifications">
                            <i class="fas fa-bell"></i>
                            الإشعارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/auth/logout" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                            <i class="fas fa-sign-out-alt"></i>
                            خروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content fade-in">
        <div class="container">
            <%- body %>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحديد الرابط النشط -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>

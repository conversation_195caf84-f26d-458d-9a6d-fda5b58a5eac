<!-- Main Content -->
    <!-- Welcome Section -->
    <div class="welcome-card fade-in">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div class="welcome-content">
                <h1 class="mb-3 welcome-title">
                    <i class="fas fa-chart-line me-3 welcome-icon"></i>
                    مرحباً بك في لوحة التحكم الذكية
                </h1>
                <p class="mb-2 welcome-subtitle">
                    <i class="fas fa-store me-2"></i>
                    إدارة شاملة ومتطورة لنظام المتجر الإلكتروني
                </p>
                <div class="welcome-stats mt-3">
                    <span class="badge bg-primary me-2">
                        <i class="fas fa-clock me-1"></i>
                        آخر تحديث: الآن
                    </span>
                    <span class="badge bg-info">
                        <i class="fas fa-user me-1"></i>
                        مدير النظام
                    </span>
                </div>
            </div>
            <div class="text-end system-status">
                <div class="status-indicator mb-3">
                    <div class="status-light"></div>
                    <div class="status-text">
                        <div class="status-title">حالة النظام</div>
                        <div class="status-value">يعمل بشكل مثالي</div>
                    </div>
                </div>
                <!-- زر إعادة تعيين رسالة الإشعارات للاختبار -->
                <div class="admin-controls">
                    <button class="btn btn-outline-primary btn-sm" onclick="resetNotificationPrompt()"
                            title="إعادة تعيين رسالة تفعيل الإشعارات"
                            data-tooltip="إعادة تعيين حالة الإشعارات">
                        <i class="fas fa-redo me-1"></i>
                        إعادة تعيين الإشعارات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .welcome-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(240, 249, 255, 0.9) 50%,
                rgba(224, 242, 254, 0.85) 100%);
            border: 1px solid rgba(14, 165, 233, 0.2);
            border-radius: var(--radius-2xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .welcome-title {
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700), var(--primary-800));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size-3xl);
        }

        .welcome-icon {
            filter: drop-shadow(0 2px 4px rgba(14, 165, 233, 0.3));
        }

        .welcome-subtitle {
            color: var(--text-secondary);
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-medium);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: rgba(34, 197, 94, 0.1);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .status-light {
            width: 12px;
            height: 12px;
            background: #22c55e;
            border-radius: 50%;
            animation: pulse 2s infinite;
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
        }

        .status-text {
            text-align: right;
        }

        .status-title {
            font-size: var(--font-size-sm);
            color: var(--text-muted);
            margin-bottom: 2px;
        }

        .status-value {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-semibold);
            color: #22c55e;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
            }
        }

        @media (max-width: 768px) {
            .welcome-card .d-flex {
                flex-direction: column;
                text-align: center;
            }

            .system-status {
                margin-top: var(--spacing-lg);
                text-align: center !important;
            }
        }
    </style>

    <!-- Statistics Cards -->
    <div class="stats-grid slide-in-right">
        <a href="/admin/customers" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card" data-tooltip="عرض وإدارة جميع العملاء">
                <div class="stat-card-header">
                    <div class="stat-icon customers-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+12%</span>
                    </div>
                </div>
                <div class="stat-number" data-count="<%= customers || 0 %>"><%= customers || 0 %></div>
                <div class="stat-label">إجمالي العملاء</div>
                <div class="stat-description">العملاء المسجلين في النظام</div>
            </div>
        </a>

        <a href="/admin/products" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card" data-tooltip="إدارة المنتجات والمخزون">
                <div class="stat-card-header">
                    <div class="stat-icon products-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+8%</span>
                    </div>
                </div>
                <div class="stat-number" data-count="<%= products || 0 %>"><%= products || 0 %></div>
                <div class="stat-label">إجمالي المنتجات</div>
                <div class="stat-description">المنتجات المتاحة في المتجر</div>
            </div>
        </a>

        <a href="/admin/orders" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card" data-tooltip="عرض جميع الطلبات">
                <div class="stat-card-header">
                    <div class="stat-icon orders-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+15%</span>
                    </div>
                </div>
                <div class="stat-number" data-count="<%= orders || 0 %>"><%= orders || 0 %></div>
                <div class="stat-label">إجمالي الطلبات</div>
                <div class="stat-description">جميع الطلبات المسجلة</div>
            </div>
        </a>

        <a href="/admin/orders/pending" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card pending-orders" data-tooltip="الطلبات التي تحتاج معالجة">
                <div class="stat-card-header">
                    <div class="stat-icon pending-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-trend urgent">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <span class="trend-value">عاجل</span>
                    </div>
                </div>
                <div class="stat-number" data-count="<%= orderspending || 0 %>"><%= orderspending || 0 %></div>
                <div class="stat-label">الطلبات المعلقة</div>
                <div class="stat-description">تحتاج إلى معالجة فورية</div>
            </div>
        </a>

        <a href="/admin/categories" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card" data-tooltip="إدارة فئات المنتجات">
                <div class="stat-card-header">
                    <div class="stat-icon categories-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-minus text-muted"></i>
                        <span class="trend-value">ثابت</span>
                    </div>
                </div>
                <div class="stat-number" data-count="<%= categories || 0 %>"><%= categories || 0 %></div>
                <div class="stat-label">إجمالي الفئات</div>
                <div class="stat-description">تصنيفات المنتجات</div>
            </div>
        </a>

        <a href="/admin/drivers" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card" data-tooltip="إدارة مندوبي التوصيل">
                <div class="stat-card-header">
                    <div class="stat-icon drivers-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="trend-value">+5%</span>
                    </div>
                </div>
                <div class="stat-number" data-count="<%= deliveryPeople || 0 %>"><%= deliveryPeople || 0 %></div>
                <div class="stat-label">مندوبي التوصيل</div>
                <div class="stat-description">فريق التوصيل النشط</div>
            </div>
        </a>

        <a href="/admin/notifications" class="text-decoration-none stat-card-link">
            <div class="stat-card enhanced-stat-card" data-tooltip="مركز الإشعارات">
                <div class="stat-card-header">
                    <div class="stat-icon notifications-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="stat-trend">
                        <div class="notification-pulse"></div>
                    </div>
                </div>
                <div class="stat-number" id="dashboardNotificationCount" data-count="0">0</div>
                <div class="stat-label">الإشعارات الجديدة</div>
                <div class="stat-description">التنبيهات والرسائل</div>
            </div>
        </a>
    </div>

    <!-- Quick Actions -->
    <div class="actions-grid bounce-in">
        <div class="action-card">
            <h5>
                <i class="fas fa-plus-circle me-2" style="color: var(--primary-color);"></i>
                إجراءات سريعة
            </h5>
            <a href="/admin/products/create" class="btn btn-custom">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </a>
            <a href="/admin/categories/create" class="btn btn-custom">
                <i class="fas fa-tags me-2"></i>إضافة فئة جديدة
            </a>
            <a href="/admin/customers" class="btn btn-custom">
                <i class="fas fa-users me-2"></i>عرض العملاء
            </a>
        </div>

        <div class="action-card">
            <h5>
                <i class="fas fa-chart-bar me-2" style="color: var(--primary-color);"></i>
                إحصائيات اليوم
            </h5>
            <div class="row text-center">
                <div class="col-6 mb-3">
                    <div class="border-end">
                        <h4 style="color: var(--primary-color);"><%= stats.totalorders || 0 %></h4>
                        <small class="text-muted">طلبات اليوم</small>
                    </div>
                </div>
                <div class="col-6">
                    <h4 style="color: var(--text-light);"><%= stats.todayPending || 0 %></h4>
                    <small class="text-muted">طلبات معلقة</small>
                </div>
                <div class="col-6 mb-3">
                    <h4 style="color: var(--secondary-color);"><%= stats.totalValue || 0 %></h4>
                    <small class="text-muted">مبيعات اليوم</small>
                </div>
                <div class="col-6">
                    <div class="border-end">
                        <h4 style="color: var(--accent-color);"><%= stats.todaycustomers || 0 %></h4>
                        <small class="text-muted">عملاء جدد</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

<style>
    /* أنماط البطاقات المحسنة */
    .enhanced-stat-card {
        background: linear-gradient(145deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(240, 249, 255, 0.9) 100%);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-2xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        transition: all var(--transition-bounce);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        height: 100%;
    }

    .stat-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-md);
    }

    .stat-trend {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 4px 8px;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.5);
    }

    .stat-trend.urgent {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-600);
        animation: pulse 2s infinite;
    }

    .stat-description {
        color: var(--text-muted);
        font-size: var(--font-size-sm);
        margin-top: var(--spacing-xs);
    }

    .notification-pulse {
        width: 10px;
        height: 10px;
        background-color: var(--danger-500);
        border-radius: 50%;
        animation: pulse 1.5s infinite;
    }

    /* أيقونات البطاقات المخصصة */
    .customers-icon {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        box-shadow: 0 8px 20px rgba(14, 165, 233, 0.4);
    }

    .products-icon {
        background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
        box-shadow: 0 8px 20px rgba(56, 189, 248, 0.4);
    }

    .orders-icon {
        background: linear-gradient(135deg, var(--primary-300), var(--primary-400));
        box-shadow: 0 8px 20px rgba(125, 211, 252, 0.4);
    }

    .pending-icon {
        background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
        box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
    }

    .categories-icon {
        background: linear-gradient(135deg, var(--primary-200), var(--primary-300));
        box-shadow: 0 8px 20px rgba(186, 230, 253, 0.4);
    }

    .drivers-icon {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        box-shadow: 0 8px 20px rgba(2, 132, 199, 0.4);
    }

    .notifications-icon {
        background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
        box-shadow: 0 8px 20px rgba(7, 89, 133, 0.4);
    }

    /* تأثير الحركة للبطاقات */
    .pending-orders {
        position: relative;
    }

    .pending-orders::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg,
            transparent 0%,
            rgba(245, 158, 11, 0.05) 50%,
            transparent 100%);
        animation: pendingGlow 3s ease-in-out infinite;
        z-index: -1;
    }

    @keyframes pendingGlow {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
    }

    /* تحسين الاستجابة */
    @media (max-width: 768px) {
        .stat-card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .stat-trend {
            align-self: flex-end;
        }
    }
</style>

<script>
// دالة إعادة تعيين رسالة تفعيل الإشعارات
function resetNotificationPrompt() {
    localStorage.removeItem('notificationPromptShown');
    localStorage.removeItem('notificationPermissionDenied');
    console.log('🔄 تم إعادة تعيين حالة رسالة الإشعارات');

    // إعادة تحميل الصفحة لإظهار الرسالة مرة أخرى
    if (confirm('سيتم إعادة تعيين حالة الإشعارات وإعادة تحميل الصفحة. هل تريد المتابعة؟')) {
        window.location.reload();
    }
}

// تحديث عداد الإشعارات في لوحة التحكم
document.addEventListener('DOMContentLoaded', function() {
    function updateDashboardNotificationCount() {
        if (window.adminNotifications) {
            const dashboardCount = document.getElementById('dashboardNotificationCount');
            if (dashboardCount) {
                dashboardCount.textContent = window.adminNotifications.notificationCount || 0;
                dashboardCount.dataset.count = window.adminNotifications.notificationCount || 0;
            }
        }
    }

    // تحديث العداد كل 5 ثوان
    setInterval(updateDashboardNotificationCount, 5000);

    // تحديث فوري عند تحميل الصفحة
    setTimeout(updateDashboardNotificationCount, 1000);

    // تأثير العد التصاعدي للأرقام
    document.querySelectorAll('.stat-number[data-count]').forEach(counter => {
        const target = parseInt(counter.dataset.count);
        const duration = 1500;
        const step = Math.max(1, Math.floor(target / (duration / 30)));
        let current = 0;

        const updateCounter = () => {
            current += step;
            if (current > target) current = target;
            counter.textContent = current;

            if (current < target) {
                requestAnimationFrame(updateCounter);
            }
        };

        setTimeout(() => {
            updateCounter();
        }, 500); // تأخير قليل للتأثير البصري
    });
});
</script>
<!-- Main Content -->
<div class="container-main">
    <div class="form-card">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 style="color: var(--primary-color);">
                    <i class="fas fa-edit me-2"></i>
                    تعديل المنتج
                </h2>
                <p class="text-muted mb-0">تعديل بيانات المنتج: <%= product.name %></p>
            </div>
            <div class="d-flex gap-2">
                <a href="/admin/products/<%= product.id %>" class="btn btn-secondary-custom">
                    <i class="fas fa-eye me-2"></i>عرض
                </a>
                <a href="/admin/products" class="btn btn-secondary-custom">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                </a>
            </div>
        </div>

        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-custom mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <%= error %>
            </div>
        <% } %>

        <form action="/admin/products/<%= product.id %>" method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag me-2"></i>اسم المنتج *
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<%= product.name %>" 
                               required placeholder="أدخل اسم المنتج">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>وصف المنتج
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="أدخل وصف المنتج"><%= product.description || '' %></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="barcode" class="form-label">
                            <i class="fas fa-barcode me-2"></i>الباركود (اختياري)
                        </label>
                        <input type="text" class="form-control" id="barcode" name="barcode"
                               value="<%= product.barcode || '' %>"
                               placeholder="أدخل رقم الباركود">
                        <small class="form-text text-muted">اتركه فارغاً إذا لم يكن متوفراً</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">
                                    <i class="fas fa-money-bill me-2"></i>السعر (ل.س) *
                                </label>
                                <input type="number" class="form-control" id="price" name="price"
                                       value="<%= product.price %>"
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-boxes me-2"></i>الكمية المتاحة *
                                </label>
                                <input type="number" class="form-control" id="quantity" name="quantity"
                                       value="<%= product.quantity %>"
                                       min="0" required placeholder="0">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="categoryId" class="form-label">
                            <i class="fas fa-list me-2"></i>الفئة *
                        </label>
                        <select class="form-select" id="categoryId" name="categoryId" required>
                            <option value="">اختر الفئة</option>
                            <% if (typeof categories !== 'undefined' && categories) { %>
                                <% categories.forEach(category => { %>
                                    <option value="<%= category.id %>" 
                                            <%= product.categoryId == category.id ? 'selected' : '' %>>
                                        <%= category.name %>
                                    </option>
                                <% }); %>
                            <% } %>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-images me-2"></i>الصور الحالية
                        </label>
                        <% if (product.images && product.images.length > 0) { %>
                            <div class="current-images d-flex flex-wrap gap-2">
                                <% product.images.forEach(image => { %>
                                    <div class="current-image position-relative" style="width: 100px; height: 100px;">
                                        <img src="<%= image.image %>" alt="<%= product.name %>" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                                        <button type="button" class="delete-image btn btn-danger btn-sm position-absolute top-0 end-0" 
                                                style="border-radius: 0 8px 0 8px; padding: 2px 6px;"
                                                onclick="deleteImage(<%= image.id %>)" title="حذف الصورة">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                <% }); %>
                            </div>
                        <% } else { %>
                            <p class="text-muted">لا توجد صور</p>
                        <% } %>
                    </div>

                    <div class="mb-3">
                        <label for="images" class="form-label">
                            <i class="fas fa-plus me-2"></i>إضافة صور جديدة
                        </label>
                        <div class="file-upload-wrapper">
                            <!-- منطقة السحب والإفلات -->
                            <div class="file-upload-area border rounded p-3 mb-2 text-center" style="background-color: rgba(178, 205, 156, 0.1); cursor: pointer;">
                                <i class="fas fa-upload fa-2x mb-2 d-block"></i>
                                <p class="mb-0">اسحب الصور هنا أو انقر لاختيار الصور</p>
                                <input type="file" class="form-control d-none" id="images" name="images" multiple accept="image/*">
                            </div>

                            <small class="form-text text-muted">يمكنك اختيار عدة صور (حد أقصى 10 صور، 5MB لكل صورة)</small>
                        </div>

                        <div id="preview-container" class="mt-3" style="display: none;">
                            <h6>الصور الجديدة:</h6>
                            <div id="image-previews" class="d-flex flex-wrap gap-2"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-3 mt-4">
                <a href="/admin/products" class="btn btn-secondary-custom">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-custom">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>


<script>
    const uploadArea = document.querySelector('.file-upload-area');
    const fileInput = document.getElementById('images');

    // فتح نافذة اختيار الملفات عند الضغط على منطقة السحب
    uploadArea.addEventListener('click', () => fileInput.click());

    // معاينة الصور
    fileInput.addEventListener('change', handleFiles);

    function handleFiles(e) {
        const files = e.target.files;
        const previewContainer = document.getElementById('preview-container');
        const imagePreviews = document.getElementById('image-previews');

        imagePreviews.innerHTML = '';

        if (files.length > 0) {
            previewContainer.style.display = 'block';

            Array.from(files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'position-relative';
                        imageDiv.innerHTML = `
                            <img src="${e.target.result}" alt="معاينة ${index + 1}"
                                 style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; border: 2px solid var(--primary-color);">
                            <small class="d-block text-center mt-1">${file.name}</small>
                        `;
                        imagePreviews.appendChild(imageDiv);
                    };
                    reader.readAsDataURL(file);
                }
            });

            // تحديث منطقة السحب برسالة تأكيد
            uploadArea.innerHTML = `
                <i class="fas fa-check-circle fa-2x mb-2" style="color: var(--primary-color);"></i>
                <p class="mb-2">تم اختيار ${files.length} صورة جديدة</p>
                <button type="button" class="btn btn-outline-primary btn-sm"
                        onclick="document.getElementById('images').click()">
                    <i class="fas fa-edit me-1"></i>تغيير الصور
                </button>
            `;
        } else {
            previewContainer.style.display = 'none';
        }
    }

    // حذف صورة موجودة
    function deleteImage(imageId) {
        if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            fetch(`/admin/products/images/${imageId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء حذف الصورة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حذف الصورة');
            });
        }
    }

    // دعم السحب والإفلات
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.style.backgroundColor = 'rgba(178, 205, 156, 0.3)';
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.style.backgroundColor = 'rgba(178, 205, 156, 0.1)';
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.style.backgroundColor = 'rgba(178, 205, 156, 0.1)';

        fileInput.files = e.dataTransfer.files;

        // تشغيل حدث التغيير
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
    });
</script>

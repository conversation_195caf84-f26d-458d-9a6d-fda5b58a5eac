/* ========== مكونات واجهة المستخدم المتقدمة ========== */

/* ========== الأزرار المحسنة ========== */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: var(--radius-lg);
    padding: 0.75rem 1.5rem;
    font-weight: var(--font-weight-semibold);
    transition: all var(--transition-normal);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    z-index: 1;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:active {
    transform: scale(0.98);
}

/* أنواع الأزرار */
.btn-primary-enhanced {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-white);
    box-shadow: var(--shadow-sm);
}

.btn-primary-enhanced:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-white);
}

.btn-success-enhanced {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: var(--text-white);
    box-shadow: var(--shadow-sm);
}

.btn-success-enhanced:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-white);
}

/* ========== البطاقات التفاعلية ========== */
.card-interactive {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    transition: all var(--transition-bounce);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.card-interactive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-400), var(--primary-600), var(--primary-400));
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

.card-interactive:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-medium);
}

.card-interactive:active {
    transform: translateY(-4px) scale(1.01);
}

/* ========== الجداول المحسنة ========== */
.table-enhanced {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.table-enhanced thead {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
}

.table-enhanced th {
    border: none;
    padding: var(--spacing-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    font-size: var(--font-size-sm);
    letter-spacing: 0.05em;
}

.table-enhanced td {
    border: none;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.table-enhanced tbody tr {
    transition: all var(--transition-fast);
}

.table-enhanced tbody tr:hover {
    background: var(--primary-50);
    transform: scale(1.01);
}

.table-enhanced tbody tr:last-child td {
    border-bottom: none;
}

/* ========== النماذج المحسنة ========== */
.form-enhanced {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.form-group-enhanced {
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.form-label-enhanced {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-control-enhanced {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    transition: all var(--transition-normal);
    font-size: var(--font-size-base);
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--primary-400);
    background: var(--bg-primary);
    box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.15);
}

.form-control-enhanced.is-invalid {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.15);
}

.form-control-enhanced.is-valid {
    border-color: var(--success-500);
    box-shadow: 0 0 0 0.2rem rgba(34, 197, 94, 0.15);
}

/* ========== الشارات والتسميات ========== */
.badge-enhanced {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--primary-700);
    border: 1px solid var(--primary-300);
}

.badge-success {
    background: linear-gradient(135deg, var(--success-100), var(--success-200));
    color: var(--success-700);
    border: 1px solid var(--success-300);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-100), var(--warning-200));
    color: var(--warning-700);
    border: 1px solid var(--warning-300);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-100), var(--danger-200));
    color: var(--danger-700);
    border: 1px solid var(--danger-300);
}

/* ========== التنبيهات المحسنة ========== */
.alert-enhanced {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid;
    position: relative;
    overflow: hidden;
}

.alert-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-success-enhanced {
    background: linear-gradient(135deg, var(--success-50), rgba(255, 255, 255, 0.8));
    border-color: var(--success-200);
    color: var(--success-700);
}

.alert-success-enhanced::before {
    background: var(--success-500);
}

.alert-warning-enhanced {
    background: linear-gradient(135deg, var(--warning-50), rgba(255, 255, 255, 0.8));
    border-color: var(--warning-200);
    color: var(--warning-700);
}

.alert-warning-enhanced::before {
    background: var(--warning-500);
}

.alert-danger-enhanced {
    background: linear-gradient(135deg, var(--danger-50), rgba(255, 255, 255, 0.8));
    border-color: var(--danger-200);
    color: var(--danger-700);
}

.alert-danger-enhanced::before {
    background: var(--danger-500);
}

.alert-icon {
    font-size: var(--font-size-lg);
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-xs);
}

/* ========== الحركات والتأثيرات ========== */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ========== فئات الحركة ========== */
.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.bounce-in {
    animation: bounceIn 1s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* ========== تحسينات الاستجابة ========== */
@media (max-width: 768px) {
    .btn-enhanced {
        padding: 0.625rem 1.25rem;
        font-size: var(--font-size-sm);
    }
    
    .card-interactive {
        padding: var(--spacing-lg);
    }
    
    .form-enhanced {
        padding: var(--spacing-xl);
    }
    
    .table-enhanced th,
    .table-enhanced td {
        padding: var(--spacing-md);
    }
}

/* ========== تحسينات إمكانية الوصول ========== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز */
.btn-enhanced:focus,
.card-interactive:focus,
.form-control-enhanced:focus {
    outline: 2px solid var(--primary-400);
    outline-offset: 2px;
}

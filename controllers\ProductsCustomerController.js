const BaseController = require('./BaseController');
const { Product, Image, Category,Favorite,Offer } = require('../models');
const path = require('path');
const fs = require('fs');
const { Op } = require('sequelize'); // ✅ هذا السطر مهم

class ProductsController extends BaseController {
    constructor() {
        super(Product, 'products');
    }

    // API: الحصول على الفئات
    async getCategory(req, res) {
        try {
            const categories = await Category.findAll({
                attributes: ['id', 'name', 'image'],
                order: [['name', 'ASC']]
            });
            
            res.json({
                success: true,
                message: 'تم جلب الفئات  بنجاح',
                data: categories
            });

        } catch (error) {
            console.error('خطأ في جلب الفئات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
     
    async getProductsByCategory(req, res) {
        try {
            const categoryId = req.params.id;
            const { page = 1, limit = 10, search = '', area = null } = req.query;

            const category = await Category.findByPk(categoryId);
            if (!category) {
                return res.status(404).json({
                    success: false,
                    message: 'الفئة غير موجودة',
                    data: null
                });
            }

            const offset = (parseInt(page) - 1) * parseInt(limit);
            const whereClause = { status: "active",
                categoryId: categoryId
             };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }


            // تنفيذ الاستعلام
            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image'],
                        required: false
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity','createdAt'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });


            res.json({
                success: true,
                message: 'تم جلب المنتجات بنجاح',
                data: {
                   /* category: {
                        id: category.id,
                        name: category.name,
                        description: category.description
                    },*/
                    products,
                    categoryname : category.name
                  /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب متاجر الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getProducts(req, res) {
        try {
            const { page = 1, limit = 10, search = ''} = req.query;

            const offset = (parseInt(page) - 1) * parseInt(limit);
            const whereClause = { status: "active"
             };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }


            // تنفيذ الاستعلام
            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image'],
                        required: false
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity','createdAt'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });


            res.json({
                success: true,
                message: 'تم جلب المنتجات بنجاح',
                data: {
                    products,
                  /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب متاجر الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // API: الحصول على تفاصيل منتج
    async getProductDetails(req, res) {
        try {
            const productId = parseInt(req.params.id);
            const customerId = parseInt(req.customer.id);
            if (isNaN(productId)) {
                return res.status(400).json({
                    success: false,
                    message: 'معرّف المنتج غير صالح',
                    data: null
                });
            }

            const product = await Product.findOne({
                where: {
                    id: productId,
                    status: "active"
                },
                attributes: ['id', 'name', 'description', 'price', 'quantity'],
                include: [
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: 'المنتج غير موجود',
                    data: null
                });
            }
            let isFavorite = false;
            if (customerId) {
            const favorite = await Favorite.findOne({
                where: {
                customer_id: customerId,
                product_id: productId
                }
            });
            isFavorite = !!favorite;
            }

            const offers = await Offer.findOne({
                where: {
                    productId: productId,
                    status: 'active'
                },
                attributes: [
                            'id', 'productId', 'discount_value', 'price_after_discount',
                            'image', 'start_date', 'end_date'
                            ],
                order: [['start_date', 'DESC']]
            });

            res.json({
                success: true,
                message: 'تم جلب تفاصيل المنتج بنجاح',
                data: {product,
                isFavorite,
                offers
                }
            });

        } catch (error) {
            console.error('خطأ في جلب تفاصيل المنتج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getProductFavorite(req, res) {
    try {
        const customerId = req.customer.id;
        const { page = 1, limit = 10, search = '' } = req.query;

        const offset = (parseInt(page) - 1) * parseInt(limit);

        const productWhereClause = {
        status: 'active',
        };

        // فلتر البحث
        if (search) {
        productWhereClause[Op.or] = [
            { name: { [Op.like]: `%${search}%` } },
            { description: { [Op.like]: `%${search}%` } }
        ];
        }

        const { count, rows: products } = await Product.findAndCountAll({
        where: productWhereClause,
        include: [
            {
            model: Favorite,
            as: 'favorites',
            where: {
                customer_id: customerId
            },
            required: true
            },
            {
            model: Image,
            as: 'images',
            attributes: ['image'],
            required: false
            }
        ],
        attributes: ['id', 'name', 'description', 'price', 'quantity', 'createdAt'],
        limit: parseInt(limit),
        offset: offset,
        order: [['createdAt', 'DESC']]
        });

         // تنسيق البيانات
            const favorite = products.map(product => ({
                id: product.id,
                name: product.name,
                description: product.description,
                 price: product.price,
                quantity: product.quantity,
                createdAt: product.createdAt,
                images: product.images ? product.images.map(image => ({
                    image: image.image
                })) : [],
               // itemsCount: order.orderDetails ? order.orderDetails.length : 0
            }));

        res.json({
        success: true,
        message: 'تم جلب المنتجات المفضلة بنجاح',
        data: {
            favorite,
           /* pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
            }*/
        }
        });

    } catch (error) {
        console.error('خطأ في جلب المنتجات المفضلة:', error);
        res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: null
        });
    }
    }


    async addFavorite(req, res) {
        try {
            const productId = parseInt(req.params.id);
            const customerId = req.customer.id;
    
            const favorite = await Favorite.findOne({
                where: {
                    product_id: productId,
                    customer_id: customerId
                }
            });
    
            if (favorite) {
                return res.status(400).json({
                    success: false,
                    message: 'المنتج مضاف مسبقاً إلى المفضلة',
                    data: null
                });
            }
    
            await Favorite.create({
                product_id: productId,
                customer_id: customerId
            });
    
            res.json({
                success: true,
                message: 'تمت إضافة المنتج إلى المفضلة بنجاح',
                data: null
            });
    
        } catch (error) {
            console.error('خطأ في إضافة المنتج إلى المفضلة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async deleteFavorite(req, res) {
        try {
            const productId = parseInt(req.params.id);
            const customerId = req.customer.id;
    
            const favorite = await Favorite.findOne({
                where: {
                    product_id: productId,
                    customer_id: customerId
                }
            });
    
            if (!favorite) {
                return res.status(400).json({
                    success: false,
                    message: 'المنتج غير موجود في المفضلة',
                    data: null
                });
            }
    
            await favorite.destroy();
    
            res.json({
                success: true,
                message: 'تم حذف المنتج من المفضلة بنجاح',
                data: null
            });
    
        } catch (error) {
            console.error('خطأ في حذف المنتج من المفضلة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

}

module.exports = new ProductsController();

const BaseController = require('./BaseController');
const { Product, Image, Category, Offer } = require('../models');
const path = require('path');
const fs = require('fs');
const { Op } = require('sequelize'); // ✅ هذا السطر مهم

class ProductsController extends BaseController {
    constructor() {
        super(Product, 'products');
    }

    async index(req, res) {
        try {
            const { page = 1, limit = 12, search = '', categoryId = null } = req.query;
            const offset = (page - 1) * limit;

            const queryOptions = {
                include: [
                    {
                        model: Category,
                        as: 'category',
                        required: false,
                        attributes: ['id', 'name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        required: false,
                        attributes: ['id', 'image']
                    }
                ],
                attributes: [
                    'id', 'name', 'description', 'price',
                    'quantity', 'categoryId', 'status', 'createdAt','rating'
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            };

            // فلترة البحث
            if (search) {
                queryOptions.where = {
                    ...queryOptions.where,
                    name: { [Op.like]: `%${search}%` }
                };
            }

            if (categoryId) {
                queryOptions.where = {
                    ...queryOptions.where,
                    categoryId
                };
            }

            const { count, rows: products } = await Product.findAndCountAll(queryOptions);
            const allproducts = await Product.findAll();
            const categories = await Category.findAll({
                attributes: ['id', 'name']
            });
            

            res.render('admin/products/index', {
                allproducts,
                products,
                categories,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: Math.ceil(count / limit),
                    totalItems: count,
                    itemsPerPage: parseInt(limit)
                },
                title: 'إدارة المنتجات'
            });

        } catch (error) {
            console.error('Error in ProductsController.index:', error);
            res.status(500).render('error', {
                error: {
                    message: 'حدث خطأ أثناء جلب المنتجات',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async create(req, res) {
        try {
            const categories = await Category.findAll({
                where: { status: 'active' },
                attributes: ['id', 'name']
            });

            res.render('admin/products/create', { 
                categories,
                title: 'إضافة منتج جديد'
            });
        } catch (error) {
            console.error('Error in ProductsController.create:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the create form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async insert(req, res) {
        try {
            const { name, price, categoryId, description, quantity, barcode } = req.body;
            if (!name || !price || !categoryId) {
                const categories = await Category.findAll({
                    where: { status: 'active' },
                    attributes: ['id', 'name']
                });
                
                return res.status(400).render('admin/products/create', {
                    error: 'جميع الحقول مطلوبة',
                    categories,
                    formData: req.body
                });
            }


            // إنشاء المنتج
            const product = await Product.create({
                name,
                price: parseFloat(price),
                categoryId: parseInt(categoryId),
                description: description || '',
                quantity: parseInt(quantity) || 0,
                barcode: barcode || null,
                status: 'active'
            });

            // معالجة رفع الصور
            if (req.files && req.files.length > 0) {
                for (const file of req.files) {
                    await Image.create({
                        productId: product.id,
                        image: `/uploads/products/${file.filename}`
                    });
                }
            }

            // إرسال إشعار للمدراء عن المنتج الجديد
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                const notification = {
                    title: 'منتج جديد',
                    body: `تم إضافة منتج جديد: ${name} - ${parseFloat(price)} ليرة`
                };

                const data = {
                    productId: product.id.toString(),
                    productName: name,
                    productPrice: parseFloat(price).toString(),
                    clickAction: `/admin/products/${product.id}`,
                    type: 'new_product'
                };

                const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
            } catch (notificationError) {
                console.error('❌ Error sending product notification:', notificationError);
                // لا نوقف العملية إذا فشل الإشعار
            }

            req.flash('success', 'تم إنشاء المنتج بنجاح');
            res.redirect('/admin/products');
        } catch (error) {
            console.error('Error in ProductsController:', error);
            const categories = await Category.findAll({
                where: { status: 'active' },
                attributes: ['id', 'name']
            });
            
            res.status(500).render('admin/products/create', {
                error: 'حدث خطأ أثناء إنشاء المنتج',
                categories,
                formData: req.body
            });
        }
    }

    async show(req, res) {
        try {
            const product = await Product.findByPk(req.params.id, {
                include: [
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['id', 'name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['id', 'image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            res.render('admin/products/show', { 
                product,
                title: `تفاصيل المنتج: ${product.name}`
            });
        } catch (error) {
            console.error('Error in ProductsController.show:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching the product',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async edit(req, res) {
        try {
            const [product, categories] = await Promise.all([
                Product.findByPk(req.params.id, {
                    include: [
                        {
                            model: Category,
                            as: 'category',
                            attributes: ['id', 'name']
                        },
                        {
                            model: Image,
                            as: 'images',
                            attributes: ['id', 'image']
                        }
                    ]
                }),
                Category.findAll({
                    where: { status: 'active' },
                    attributes: ['id', 'name']
                })
            ]);

            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            res.render('admin/products/edit', { 
                product, 
                categories,
                title: `تعديل المنتج: ${product.name}`
            });
        } catch (error) {
            console.error('Error in ProductsController.edit:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the edit form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async update(req, res) {
        try {
            const product = await Product.findByPk(req.params.id);
            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            const { name, price, categoryId, description, quantity, barcode } = req.body;

            const oldQuantity = product.quantity;
            const newQuantity = parseInt(quantity) || 0;

            await product.update({
                name,
                price: parseFloat(price),
                categoryId: parseInt(categoryId),
                description: description || '',
                quantity: newQuantity,
                barcode: barcode || null
            });

            // إرسال إشعار عند نفاد المخزون أو انخفاضه
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                // إشعار عند نفاد المخزون
                if (oldQuantity > 0 && newQuantity === 0) {
                    const notification = {
                        title: '⚠️ نفاد مخزون',
                        body: `نفد مخزون المنتج: ${name}`
                    };

                    const data = {
                        productId: product.id.toString(),
                        productName: name,
                        oldQuantity: oldQuantity.toString(),
                        newQuantity: newQuantity.toString(),
                        clickAction: `/admin/products/${product.id}`,
                        type: 'stock_out'
                    };

                    const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
                }
                // إشعار عند انخفاض المخزون (أقل من 5)
                else if (newQuantity > 0 && newQuantity <= 5 && oldQuantity > 5) {
                    const notification = {
                        title: '⚠️ انخفاض مخزون',
                        body: `المخزون منخفض للمنتج: ${name} (${newQuantity} قطع متبقية)`
                    };

                    const data = {
                        productId: product.id.toString(),
                        productName: name,
                        oldQuantity: oldQuantity.toString(),
                        newQuantity: newQuantity.toString(),
                        clickAction: `/admin/products/${product.id}`,
                        type: 'low_stock'
                    };

                    const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
                }
            } catch (notificationError) {
                console.error('❌ Error sending stock notification:', notificationError);
                // لا نوقف العملية إذا فشل الإشعار
            }

            // رفع الصور الجديدة
            if (req.files && req.files.length > 0) {
                for (const file of req.files) {
                    await Image.create({
                        productId: product.id,
                        image: `/uploads/products/${file.filename}`
                    });
                }
            }

            req.flash('success', 'تم تحديث المنتج بنجاح');
            res.redirect('/admin/products');
        } catch (error) {
            console.error('Error in ProductsController.update:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while updating the product',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async destroy(req, res) {
        try {
            const product = await Product.findByPk(req.params.id, {
                include: [{ model: Image, as: 'images' }]
            });

            if (!product) {
                return res.status(404).json({ success: false, message: 'المنتج غير موجود' });
            }

            // حذف الصور من السيرفر
            for (const image of product.images) {
                const imagePath = path.join(__dirname, '..', 'public', image.image);
                if (fs.existsSync(imagePath)) {
                    fs.unlinkSync(imagePath); // حذف الملف
                }
            }

            // حذف الصور من الجدول
            await Image.destroy({ where: { productId: product.id } });

            // حذف المنتج نفسه
            await product.destroy();

            return res.json({ success: true, message: 'تم حذف المنتج وجميع صوره بنجاح' });

        } catch (error) {
            console.error('Error in ProductsController.destroy:', error);
            return res.status(500).json({
                success: false,
                message: 'حدث خطأ أثناء حذف المنتج'
            });
        }
    }

    // حذف صورة منتج
    async deleteImage(req, res) {
        try {
            const imageId = req.params.id;
            const image = await Image.findByPk(imageId);

            if (!image) {
                return res.status(404).json({
                    success: false,
                    message: 'الصورة غير موجودة'
                });
            }

            // حذف الملف من النظام
            const fs = require('fs');
            const imagePath = path.join(__dirname, '..', 'public', image.image);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }

            // حذف السجل من قاعدة البيانات
            await image.destroy();

            res.json({
                success: true,
                message: 'تم حذف الصورة بنجاح'
            });

        } catch (error) {
            console.error('Error deleting image:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ أثناء حذف الصورة'
            });
        }
    }

    async addoffer(req, res)  {
        const product = await Product.findByPk(req.params.id);
        if (!product) return res.status(404).send('المنتج غير موجود');
        res.render('admin/products/addoffer', { product });
    }


    async createoffer(req, res) {
        try {
            const product = await Product.findByPk(req.params.id);
            if (!product) return res.status(404).send('المنتج غير موجود');

            const imageUrl = `/uploads/offers/${req.file.filename}`;

            await Offer.create({
            productId: product.id,
            image: imageUrl,
            discount_value: req.body.discount_value,
            price_after_discount: req.body.price_after_discount,
            start_date: req.body.start_date,
            end_date: req.body.end_date,
            status: req.body.status || 'active'
            });

            res.redirect('/admin/offers');
        } catch (error) {
            console.error('خطأ أثناء إضافة العرض:', error);
            res.status(500).send('حدث خطأ في الخادم');
        }
    }


    
    async deleteoffer(req, res) {
        try {
            const productId = req.params.id;
            // الحصول على العرض المرتبط بالمنتج
            const offer = await Offer.findOne({ where: { productId: productId } });

            if (!offer || !offer.image) {
            return res.status(404).send('العرض غير موجود');
            }

            const imagePath = path.join(__dirname, '../../public', offer.image);

            // حذف الصورة من السيرفر
            if (fs.existsSync(imagePath)) {
            fs.unlinkSync(imagePath);
            }

            // حذف مرجع الصورة من قاعدة البيانات
            offer.destroy();
            
            res.redirect(`/admin/offers/`);
        } catch (error) {
            console.error('خطأ أثناء حذف صورة العرض:', error);
            res.status(500).send('حدث خطأ أثناء حذف صورة العرض');
        }
    } 




   async showoffers(req, res) {
        try {
            const { status, date } = req.query;

            const offerWhere = {};

            // فلترة حسب الحالة
            if (status && ['active', 'inactive'].includes(status)) {
                offerWhere.status = status;
            }

            // فلترة حسب التاريخ
            if (date) {
                const currentDate = new Date(date);
                offerWhere.start_date = { [Op.lte]: currentDate };
                offerWhere.end_date = { [Op.gte]: currentDate };
            }

            const offers = await Offer.findAll({
                where: offerWhere,
                include: [
                    {
                        model: Product,
                        as: 'product',
                        attributes: [
                            'id', 'name', 'description', 'price',
                            'quantity', 'categoryId', 'status', 'createdAt'
                        ],
                        include: [
                            {
                                model: Category,
                                as: 'category',
                                attributes: ['id', 'name'],
                                required: false
                            }
                        ]
                    }
                ],
                order: [['start_date', 'DESC']]
            });

            res.render('admin/products/showoffers', {
                products: offers,
                title: 'العروض',
                filters: { status, date }
            });
        } catch (error) {
            console.error('خطأ في showoffers:', error);
            res.status(500).render('error', {
                error: {
                    message: 'حدث خطأ أثناء جلب العروض',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }



    // عرض نموذج التعديل
    async editOffer(req, res) {
        try {
        const offer = await Offer.findByPk(req.params.id);
        
        if (!offer) {
            return res.status(404).send('العرض غير موجود');
        }
        const product = await Product.findOne({ where: { id: offer.productId } });
        res.render('admin/products/editoffer', { product,offer });
        } catch (error) {
        console.error('خطأ في عرض صفحة تعديل العرض:', error);
        res.status(500).send('حدث خطأ');
        }
    }

    // تنفيذ التعديل
   
    async updateOffer(req, res) {
        try {
            const offerId = req.params.id;
            const { discount_value, price_after_discount, start_date, end_date, status } = req.body;
            const offer = await Offer.findByPk(offerId);
            if (!offer) {
            return res.status(404).send('العرض غير موجود');
            }

            // إذا تم رفع صورة جديدة، نحذف القديمة ونخزن الجديدة
            if (req.file) {
            // حذف الصورة القديمة من السيرفر (إن وُجدت)
            if (offer.image) {
                const oldImagePath = path.join(__dirname, '..', 'public', 'uploads', offer.image);
                if (fs.existsSync(oldImagePath)) {
                fs.unlinkSync(oldImagePath);
                }
            }

            offer.image = req.file.filename;
            }

            // تحديث باقي الحقول
            await offer.update({
            discount_value,
            price_after_discount,
            start_date,
            end_date,
            status,
            image: offer.image // سواء تم تحديثها أو بقيت كما هي
            });

            res.redirect('/admin/offers'); // أو أي مسار عرض بعد التحديث
        } catch (error) {
            console.error('خطأ في تعديل العرض:', error);
            res.status(500).send('حدث خطأ أثناء التعديل');
        }
    }


}

module.exports = new ProductsController();

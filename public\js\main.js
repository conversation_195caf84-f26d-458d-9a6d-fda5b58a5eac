/**
 * نظام إدارة المتاجر الذكي - JavaScript محدث
 * تحسينات التفاعل والتجربة المستخدم مع النظام الجديد
 */

/**
 * تهيئة النظام العام
 */
function initializeSystem() {
    // إخفاء شاشة التحميل
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 300);
        }, 1000);
    }

    // تهيئة الشريط الجانبي للجوال
    initializeMobileSidebar();

    // تهيئة القوائم المنسدلة
    initializeDropdowns();

    // تهيئة التنبيهات التلقائية
    initializeAutoAlerts();
}
/**
 * تهيئة الشريط الجانبي للجوال
 */
function initializeMobileSidebar() {
    const sidebarToggle = document.querySelector('.navbar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            if (overlay) {
                overlay.classList.toggle('show');
            }
        });

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            });
        }
    }
}

/**
 * تهيئة القوائم المنسدلة - معطلة لتجنب التضارب مع Bootstrap
 */
function initializeDropdowns() {
    // تم تعطيل هذه الوظيفة لتجنب التضارب مع Bootstrap Dropdowns
    // Bootstrap يتولى إدارة القوائم المنسدلة تلقائياً
    console.log('✅ تم تخطي تهيئة القوائم المنسدلة - Bootstrap يتولى الإدارة');
}

/**
 * تهيئة تنبيهات تلقائية (Auto Alerts)
 */
function initializeAutoAlerts() {
    const alerts = document.querySelectorAll('.alert');

    alerts.forEach(alert => {
        // إخفاء تلقائي بعد 5 ثواني
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 500);
        }, 5000);

        // زر إغلاق التنبيه (إن وجد)
        const closeBtn = alert.querySelector('[data-bs-dismiss="alert"]');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => alert.remove());
        }
    });
}

/**
 * تهيئة تأثيرات الظهور عند التمرير (Animations)
 */
function initializeAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('appear');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach(el => observer.observe(el));
}

/**
 * تهيئة نماذج الإدخال
 */
function initializeForms() {
    const inputs = document.querySelectorAll('input, textarea, select');

    inputs.forEach(input => {
        // إضافة أو إزالة فئة 'focused' حسب التركيز
        input.addEventListener('focus', () => input.classList.add('focused'));
        input.addEventListener('blur', () => input.classList.remove('focused'));

        // تحقق مباشر من صحة البيانات أثناء الكتابة (اختياري)
        input.addEventListener('input', () => {
            if (input.checkValidity()) {
                input.classList.remove('invalid');
            } else {
                input.classList.add('invalid');
            }
        });
    });

    // مثال: منع إرسال النموذج إذا كان غير صالح (يمكن تعديله حسب الحاجة)
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                alert('يرجى تعبئة جميع الحقول المطلوبة بشكل صحيح.');
            }
        });
    });
}

/**
 * تهيئة الجداول مع دعم الترتيب
 */
function initializeTables() {
    const tables = document.querySelectorAll('.sortable-table');

    tables.forEach(table => {
        const headers = table.querySelectorAll('thead th');
        headers.forEach((th, index) => {
            th.style.cursor = 'pointer';
            th.addEventListener('click', () => sortTable(table, index));
        });
    });
}

function sortTable(table, colIndex) {
    const tbody = table.tBodies[0];
    const rows = Array.from(tbody.rows);
    const isNumeric = rows.every(row => !isNaN(parseFloat(row.cells[colIndex].textContent.trim())));
    let ascending = !table.dataset.sortAsc || table.dataset.sortCol != colIndex;

    rows.sort((a, b) => {
        let aText = a.cells[colIndex].textContent.trim();
        let bText = b.cells[colIndex].textContent.trim();

        if (isNumeric) {
            return ascending
                ? parseFloat(aText) - parseFloat(bText)
                : parseFloat(bText) - parseFloat(aText);
        } else {
            return ascending
                ? aText.localeCompare(bText)
                : bText.localeCompare(aText);
        }
    });

    // إعادة ترتيب الصفوف في tbody
    rows.forEach(row => tbody.appendChild(row));
    table.dataset.sortAsc = ascending;
    table.dataset.sortCol = colIndex;
}

/**
 * نظام إشعارات متكامل
 */
const Notifications = {
    container: null,
    soundEnabled: false,
    soundSrc: null,

    init(containerSelector = '#notifications-container', soundSrc = null) {
        this.container = document.querySelector(containerSelector);
        this.soundSrc = soundSrc;
    },

    addNotification({ title = '', message = '', type = 'info', timeout = 5000 }) {
        if (!this.container) return;

        const notif = document.createElement('div');
        notif.className = `notification ${type}`;
        notif.innerHTML = `
            <strong>${title}</strong>
            <p>${message}</p>
            <button class="close-btn" aria-label="إغلاق">&times;</button>
        `;

        this.container.appendChild(notif);

        const closeBtn = notif.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => notif.remove());

        if (this.soundEnabled && this.soundSrc) {
            this.playSound();
        }

        if (timeout > 0) {
            setTimeout(() => {
                notif.style.opacity = '0';
                setTimeout(() => notif.remove(), 500);
            }, timeout);
        }
    },

    playSound() {
        if (!this.soundSrc) return;
        const audio = new Audio(this.soundSrc);
        audio.play();
    },

    clearAll() {
        if (!this.container) return;
        this.container.innerHTML = '';
    }
};

/**
 * مكتبة مساعدة SmartStore
 */
const SmartStore = {
    formatNumber(num) {
        return num.toLocaleString('ar-EG');
    },
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-EG');
    },
    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
};

// تهيئة كل شيء عند تحميل DOM
document.addEventListener('DOMContentLoaded', function() {
    initializeMobileSidebar();
    initializeDropdowns();
    initializeAutoAlerts();
   // initializeAnimations();
    initializeForms();
    initializeTables();
    Notifications.init(); // حدد selector وملف صوتي إن أردت
});

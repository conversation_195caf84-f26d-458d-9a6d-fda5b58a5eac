<!-- صفحة إدارة المنتجات -->

<style>
    .product-card {
        background: var(--white);
        border-radius: 16px;
        box-shadow: var(--shadow);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        overflow: hidden;
        padding: 1rem;
    }

    .product-card:hover {
        transform: translateY(-5px) scale(1.01);
        box-shadow: var(--shadow-lg);
    }

    .product-image {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        object-fit: cover;
        border: 2px solid var(--bg-light);
    }

    .btn-custom {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        color: var(--white);
        padding: 0.5rem 1.2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
    }
    
     .bg-primary {
        background-color: #598ddb !important;
        width: fit-content;
    }

    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
        color: var(--white);
    }

    .badge-custom {
        background: var(--primary-color);
        color: var(--white);
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .price-tag {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        padding: 0.4rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .page-header {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
    }

    .stats-card {
       background: linear-gradient(135deg, #abc0ca, #056191);
        color: var(--white);
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
        box-shadow: var(--shadow);
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px) scale(1.02);
    }

    .search-box {
        background: var(--white);
        border: 2px solid var(--bg-light);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
    }

    .btn-sm {
        padding: 0.35rem 0.6rem;
        font-size: 0.85rem;
        border-radius: 6px;
    }

    .badge.bg-success,
    .badge.bg-secondary {
        border-radius: 15px;
        padding: 0.4rem 0.7rem;
        font-weight: 500;
        transition: background 0.3s ease;
    }

    .badge.bg-success:hover {
        background-color: #28a745 !important;
    }

    .badge.bg-secondary:hover {
        background-color: #6c757d !important;
    }

    @media (max-width: 768px) {
        .product-image {
            width: 80px;
            height: 80px;
        }
        .product-card {
            padding: 0.8rem;
        }
    }

    .pagination .page-item .page-link {
        border-radius: 8px;
        color: var(--primary-color);
        transition: all 0.3s ease;
    }
    .pagination .page-item .page-link:hover {
        background-color: var(--primary-color);
        color: white;
    }
    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* أنماط البحث والفلترة المحسنة */
    .search-filter-container {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border-light);
    }

    .search-input-wrapper {
        position: relative;
    }

    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        z-index: 2;
    }

    .enhanced-search {
        padding-right: 45px;
        border: 2px solid var(--border-light);
        border-radius: var(--radius-lg);
        transition: all var(--transition-normal);
        background: var(--bg-secondary);
    }

    .enhanced-search:focus {
        border-color: var(--primary-400);
        box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.15);
        background: var(--white);
    }

    .filter-wrapper {
        position: relative;
    }

    .enhanced-filter {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        padding-left: 35px;
        border: 2px solid var(--border-light);
        border-radius: var(--radius-lg);
        transition: all var(--transition-normal);
        background: var(--bg-secondary);
    }

    .enhanced-filter:focus {
        border-color: var(--primary-400);
        box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.15);
        background: var(--white);
    }

    .filter-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        pointer-events: none;
        font-size: 0.8rem;
    }

    .search-results-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-light);
    }

    .results-count {
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
    }

    .view-options {
        display: flex;
        gap: 5px;
    }

    .view-options .btn {
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-md);
    }

    .view-options .btn.active {
        background-color: var(--primary-500);
        border-color: var(--primary-500);
        color: white;
    }

    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        display: none;
    }

    .suggestion-item {
        padding: 10px 15px;
        cursor: pointer;
        transition: background-color var(--transition-fast);
    }

    .suggestion-item:hover {
        background-color: var(--bg-secondary);
    }

    /* تحسين البطاقات للعرض المختلف */
    .products-list-view .product-item {
        width: 100%;
    }

    .products-list-view .product-card {
        display: flex;
        align-items: center;
        padding: var(--spacing-lg);
    }

    .products-list-view .product-image {
        width: 80px;
        height: 80px;
        margin-left: var(--spacing-lg);
    }

    @media (max-width: 768px) {
        .search-results-info {
            flex-direction: column;
            gap: var(--spacing-sm);
            align-items: flex-start;
        }

        .view-options {
            align-self: flex-end;
        }
    }
</style>

<!-- محتوى الصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    <i class="fas fa-box text-primary me-2"></i>
                    إدارة المنتجات
                </h1>
                <p class="text-muted mb-0">إدارة وتنظيم منتجات المتجر</p>
            </div>
            <div>
                <a href="/admin/products/create" class="btn btn-custom">
                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.length %></div>
                <div class="small">إجمالي المنتجات</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.filter(p => p.quantity > 0).length %></div>
                <div class="small">متوفر في المخزن</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.filter(p => p.quantity === 0).length %></div>
                <div class="small">نفد من المخزن</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.filter(p => p.status === 'active').length %></div>
                <div class="small">منتجات نشطة</div>
            </div>
        </div>
    </div>

    <!-- البحث والتصفية المحسن -->
    <div class="search-filter-container mb-4">
        <div class="row g-3">
            <div class="col-md-5">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control search-box enhanced-search"
                           placeholder="البحث في المنتجات..." id="searchInput">
                    <div class="search-suggestions" id="searchSuggestions"></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-wrapper">
                    <select class="form-select search-box enhanced-filter" id="categoryFilter">
                        <option value="">جميع الفئات</option>
                        <% if (categories) { %>
                            <% categories.forEach(category => { %>
                                <option value="<%= category.id %>"><%= category.name %></option>
                            <% }); %>
                        <% } %>
                    </select>
                    <i class="fas fa-chevron-down filter-icon"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-wrapper">
                    <select class="form-select search-box enhanced-filter" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="out-of-stock">نفد من المخزن</option>
                    </select>
                    <i class="fas fa-chevron-down filter-icon"></i>
                </div>
            </div>
            <div class="col-md-1">
                <button class="btn btn-outline-secondary" id="clearFilters" title="مسح الفلاتر">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- مؤشر النتائج -->
        <div class="search-results-info mt-3" id="searchResultsInfo">
            <span class="results-count">عرض جميع المنتجات</span>
            <div class="view-options">
                <button class="btn btn-sm btn-outline-primary active" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary" data-view="list">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- المنتجات -->
    <div class="row" id="productsContainer">
        <% if (products && products.length > 0) { %>
            <% products.forEach(product => { %>
                <div class="col-lg-4 col-md-6 mb-4 product-item"
                    data-name="<%= product.name %>"
                    data-category="<%= product.categoryId %>"
                    data-status="<%= product.status === 'active' ? 'active' : 'inactive' %><%= product.stock === 0 ? ' out-of-stock' : '' %>">
                    <div class="product-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <% if (product.images && product.images.length > 0) { %>
                                    <img src="<%= product.images[0].image %>" alt="<%= product.name %>" class="product-image me-3">
                                <% } else { %>
                                    <div class="product-image me-3 d-flex align-items-center justify-content-center bg-light">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <% } %>
                                <div class="flex-grow-1">
                                    <h5 class="card-title mb-2"><%= product.name %></h5>
                                    <p class="card-text text-muted small mb-2"><%= product.description %></p>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span class="price-tag"><%= product.price %> ل.س</span>
                                        <span class="badge <%= product.status === 'active' ? 'bg-success' : 'bg-danger' %>">
                                            <%= product.status === 'active' ? 'نشط' : 'غير نشط' %>
                                        </span>
                                        <span class="badge bg-primary %>">
                                            <%= product.category ? product.category.name : 'غير محدد' %>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="small text-muted">المبيعات</div>
                                    <div class="fw-bold text-primary"><%= product.salesCount || 0 %></div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">التقييم</div>
                                    <div class="fw-bold text-warning">
                                        <i class="fas fa-star"></i> <%= product.rating || 0 %>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">الكمية</div>
                                    <div class="fw-bold text-info"><%= product.quantity %></div>
                                </div>
                            </div>


                            <div class="d-flex gap-2">
                                <a href="/admin/products/<%= product.id %>" class="btn btn-outline-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="/admin/products/<%= product.id %>/edit" class="btn btn-outline-success btn-sm flex-fill">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteProduct(<%= product.id %>)">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <a href="/admin/products/<%= product.id %>/offer" class="btn btn-outline-warning btn-sm flex-fill">
                                    <i class="fas fa-star me-1"></i>إضافة عرض
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-box fa-5x text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد منتجات</h3>
                    <p class="text-muted">ابدأ بإضافة منتجات جديدة لمتجرك</p>
                    <a href="/admin/products/create" class="btn btn-custom">
                        <i class="fas fa-plus me-2"></i>إضافة أول منتج
                    </a>
                </div>
            </div>
        <% } %>
    </div>

    <!-- الباجينيشن -->
    <% if (products && products.length > 0) { %>
        <nav aria-label="صفحات المنتجات" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                    <li class="page-item <%= pagination.currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>">
                            <%= i %>
                        </a>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر البحث والفلترة
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const statusFilter = document.getElementById('statusFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const resultsInfo = document.getElementById('searchResultsInfo');
    const viewButtons = document.querySelectorAll('[data-view]');
    const productsContainer = document.getElementById('productsContainer');

    // إعداد مستمعي الأحداث
    searchInput.addEventListener('input', debounce(filterProducts, 300));
    categoryFilter.addEventListener('change', filterProducts);
    statusFilter.addEventListener('change', filterProducts);
    clearFiltersBtn.addEventListener('click', clearAllFilters);

    // تبديل العرض
    viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.dataset.view;
            toggleView(view);

            // تحديث الأزرار النشطة
            viewButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // البحث والفلترة المحسن
    function filterProducts() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const products = document.querySelectorAll('.product-item');

        let visibleCount = 0;
        let totalCount = products.length;

        products.forEach(product => {
            const name = product.dataset.name.toLowerCase();
            const category = product.dataset.category;
            const status = product.dataset.status;

            const matchesSearch = !searchTerm || name.includes(searchTerm);
            const matchesCategory = !categoryFilter || category === categoryFilter;
            const matchesStatus = !statusFilter || status.includes(statusFilter);

            const shouldShow = matchesSearch && matchesCategory && matchesStatus;

            if (shouldShow) {
                product.style.display = '';
                product.style.animation = 'fadeInUp 0.3s ease';
                visibleCount++;
            } else {
                product.style.display = 'none';
            }
        });

        // تحديث معلومات النتائج
        updateResultsInfo(visibleCount, totalCount);

        // إظهار رسالة عدم وجود نتائج
        showNoResultsMessage(visibleCount);
    }

    // تحديث معلومات النتائج
    function updateResultsInfo(visible, total) {
        const resultsCount = resultsInfo.querySelector('.results-count');
        if (visible === total) {
            resultsCount.textContent = `عرض جميع المنتجات (${total})`;
        } else {
            resultsCount.textContent = `عرض ${visible} من ${total} منتج`;
        }
    }

    // إظهار رسالة عدم وجود نتائج
    function showNoResultsMessage(count) {
        let noResultsMsg = document.getElementById('noResultsMessage');

        if (count === 0) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'noResultsMessage';
                noResultsMsg.className = 'col-12 text-center py-5';
                noResultsMsg.innerHTML = `
                    <div class="no-results-content">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
                        <button class="btn btn-outline-primary" onclick="clearAllFilters()">
                            <i class="fas fa-times me-2"></i>مسح الفلاتر
                        </button>
                    </div>
                `;
                productsContainer.appendChild(noResultsMsg);
            }
            noResultsMsg.style.display = 'block';
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    // مسح جميع الفلاتر
    function clearAllFilters() {
        searchInput.value = '';
        categoryFilter.value = '';
        statusFilter.value = '';
        filterProducts();

        // تأثير بصري
        clearFiltersBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            clearFiltersBtn.style.transform = '';
        }, 150);
    }

    // تبديل العرض بين الشبكة والقائمة
    function toggleView(view) {
        if (view === 'list') {
            productsContainer.classList.add('products-list-view');
            productsContainer.classList.remove('row');
        } else {
            productsContainer.classList.remove('products-list-view');
            productsContainer.classList.add('row');
        }
    }

    // دالة التأخير للبحث
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // تحسين تفاعل البطاقات
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });

    // إضافة تأثير التحميل للأزرار
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled && !this.classList.contains('btn-outline-danger')) {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            }
        });
    });
});

// حذف المنتج مع تحسينات
function deleteProduct(productId) {
    // إظهار مربع حوار تأكيد مخصص
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const deleteBtn = event.target.closest('button');
        const originalContent = deleteBtn.innerHTML;

        // إضافة حالة التحميل
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        fetch(`/admin/products/${productId}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تأثير الاختفاء
                const productCard = deleteBtn.closest('.product-item');
                productCard.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    location.reload();
                }, 300);
            } else {
                throw new Error(data.message || 'فشل في حذف المنتج');
            }
        })
        .catch(error => {
            console.error('خطأ في حذف المنتج:', error);
            alert('حدث خطأ أثناء حذف المنتج: ' + error.message);

            // استعادة الزر
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalContent;
        });
    }
}

// إضافة أنماط CSS للحركات
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeOut {
        from { opacity: 1; transform: scale(1); }
        to { opacity: 0; transform: scale(0.8); }
    }

    .no-results-content {
        padding: 2rem;
        border-radius: var(--radius-xl);
        background: var(--bg-secondary);
    }
`;
document.head.appendChild(style);
</script>

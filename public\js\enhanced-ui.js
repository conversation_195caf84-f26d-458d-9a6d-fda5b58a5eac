/**
 * نظام واجهة المستخدم المحسن
 * Enhanced UI System for Store Management
 */

class EnhancedUI {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.initializeAnimations();
    }

    init() {
        console.log('🎨 تهيئة نظام واجهة المستخدم المحسن...');
        this.addLoadingStates();
        this.enhanceCards();
        this.setupSearchAndFilter();
        this.initializeTooltips();
    }

    setupEventListeners() {
        // تحسين تفاعل البطاقات
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', this.handleCardHover.bind(this));
            card.addEventListener('mouseleave', this.handleCardLeave.bind(this));
            card.addEventListener('click', this.handleCardClick.bind(this));
        });

        // تحسين الأزرار
      /*  document.querySelectorAll('.btn-custom').forEach(btn => {
            btn.addEventListener('click', this.handleButtonClick.bind(this));
        });*/

        // تحسين النماذج
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        });

        // تحسين البحث
       document.querySelectorAll('input[type="search"], .search-box').forEach(input => {
            input.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        });
    }

    handleCardHover(event) {
        const card = event.currentTarget;
        const icon = card.querySelector('.stat-icon');
        
        if (icon) {
            icon.style.transform = 'scale(1.1) rotate(5deg)';
        }

        // إضافة تأثير الضوء
        this.addGlowEffect(card);
    }

    handleCardLeave(event) {
        const card = event.currentTarget;
        const icon = card.querySelector('.stat-icon');
        
        if (icon) {
            icon.style.transform = 'scale(1) rotate(0deg)';
        }

        // إزالة تأثير الضوء
        this.removeGlowEffect(card);
    }

    handleCardClick(event) {
        const card = event.currentTarget;
        const link = card.querySelector('a');
        
        if (link && !event.target.closest('button')) {
            // تأثير النقر
            card.style.transform = 'scale(0.98)';
            setTimeout(() => {
                card.style.transform = '';
                window.location.href = link.href;
            }, 150);
        }
    }

    handleButtonClick(event) {
        const button = event.currentTarget;
        
        // تأثير الموجة
        this.createRippleEffect(button, event);
        
        // تأثير التحميل
        if (!button.disabled) {
            this.addLoadingState(button);
        }
    }

    handleFormSubmit(event) {
        const form = event.currentTarget;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (submitBtn) {
            this.addLoadingState(submitBtn);
        }

        // التحقق من صحة النموذج
        if (!this.validateForm(form)) {
            event.preventDefault();
            this.removeLoadingState(submitBtn);
        }
    }

    handleSearch(event) {
        const input = event.target;
        const searchTerm = input.value.toLowerCase();
        
        // البحث في البطاقات
        this.filterCards(searchTerm);
        
        // البحث في الجداول
        this.filterTables(searchTerm);
    }

    addGlowEffect(element) {
        element.style.boxShadow = `
            0 20px 40px rgba(14, 165, 233, 0.25),
            0 8px 30px rgba(56, 189, 248, 0.2),
            0 0 0 1px rgba(14, 165, 233, 0.1) inset
        `;
    }

    removeGlowEffect(element) {
        element.style.boxShadow = '';
    }

    createRippleEffect(button, event) {
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }

    addLoadingState(button) {
        if (button.dataset.loading === 'true') return;
        
        button.dataset.loading = 'true';
        button.dataset.originalText = button.innerHTML;
        button.disabled = true;
        
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            جاري المعالجة...
        `;
    }

    removeLoadingState(button) {
        if (button.dataset.loading !== 'true') return;
        
        button.disabled = false;
        button.innerHTML = button.dataset.originalText;
        delete button.dataset.loading;
        delete button.dataset.originalText;
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });
        
        return isValid;
    }

    showFieldError(input, message) {
        this.clearFieldError(input);
        
        input.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);
    }

    clearFieldError(input) {
        input.classList.remove('is-invalid');
        const errorDiv = input.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    filterCards(searchTerm) {
        document.querySelectorAll('.stat-card, .product-card, .action-card').forEach(card => {
            const text = card.textContent.toLowerCase();
            const shouldShow = text.includes(searchTerm);
            
            card.style.display = shouldShow ? '' : 'none';
            
            if (shouldShow && searchTerm) {
                card.style.animation = 'fadeInUp 0.3s ease';
            }
        });
    }

    filterTables(searchTerm) {
        document.querySelectorAll('table tbody tr').forEach(row => {
            const text = row.textContent.toLowerCase();
            const shouldShow = text.includes(searchTerm);
            
            row.style.display = shouldShow ? '' : 'none';
        });
    }

    initializeAnimations() {
        // إضافة CSS للحركات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to { transform: scale(4); opacity: 0; }
            }
            
            @keyframes fadeInUp {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
        document.head.appendChild(style);
    }

    initializeTooltips() {
        // تهيئة التلميحات
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    showTooltip(event) {
        const element = event.currentTarget;
        const text = element.dataset.tooltip;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        setTimeout(() => tooltip.style.opacity = '1', 10);
        
        element._tooltip = tooltip;
    }

    hideTooltip(event) {
        const element = event.currentTarget;
        if (element._tooltip) {
            element._tooltip.remove();
            delete element._tooltip;
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    addLoadingStates() {
        // إضافة حالات التحميل للعناصر
        document.querySelectorAll('.stat-card').forEach(card => {
            card.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
    }

    enhanceCards() {
        // تحسين البطاقات
        document.querySelectorAll('.stat-card').forEach(card => {
            card.style.cursor = 'pointer';
            card.setAttribute('tabindex', '0');
        });
    }

    setupSearchAndFilter() {
        // إعداد البحث والفلترة
        const searchInputs = document.querySelectorAll('.search-box');
        searchInputs.forEach(input => {
            input.style.transition = 'all 0.3s ease';
        });
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedUI = new EnhancedUI();
});

// تصدير للاستخدام العام
window.EnhancedUI = EnhancedUI;

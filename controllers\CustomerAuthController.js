const { Customer, DeliveryPerson, Order,CustomerToken,DriverToken } = require('../models');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const NodeGeocoder = require('node-geocoder');

class CustomerApiController {
    
    /**
     * تسجيل دخول العميل
     * POST /api/customers/login
     */
   async login(req, res) {
    try {
        const { name, password } = req.body;

        if (!name || !password) {
            return res.status(400).json({
                success: false,
                message: 'البريد الإلكتروني أو رقم الهاتف وكلمة المرور مطلوبان',
                data: null
            });
        }

        const customer = await Customer.findOne({
            where: {
                [Op.or]: [
                    { name },
                    { phoneNumber: name }
                ]
            }
        });

        const driver = await DeliveryPerson.findOne({
            where: {
                [Op.or]: [
                    { username: name },
                    { phoneNumber: name }
                ]
            }
        });
        if (!customer && !driver) {
            return res.status(401).json({
                success: false,
                message: 'بيانات تسجيل الدخول غير صحيحة',
                data: null
            });
        }
        const isValidPassword = customer ? await customer.validatePassword(password) : false;
        const isValidPassworddelivery = driver ? await bcrypt.compare(password, driver.password) : false;
        if (!isValidPassword && !isValidPassworddelivery) {
            return res.status(401).json({
                success: false,
                message: 'بيانات تسجيل الدخول غير صحيحة',
                data: null
            });
        }

        let access_token = null;

        if (customer && isValidPassword) {
            access_token = jwt.sign(
                {
                    customerId: customer.id,
                    name: customer.name,
                    type: 'customer'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );

            // تحقق هل العميل لديه توكن سابق (تسجيل دخول من جهاز آخر)
          /*  if (customer.currentToken && customer.currentToken !== access_token) {
                // تقدر ترفض الطلب أو تستبدل التوكن السابق بهذا الجديد
                // مثلا ترفض هنا:
                return res.status(403).json({
                    success: false,
                    message: 'مستخدم مسجل دخول من جهاز آخر',
                    data: null
                });
            }*/

            // أو مباشرة تحدث التوكن الحالي ليكون هذا التوكن الجديد
            customer.currentToken = access_token;
            await customer.save();
        }
        else if (driver && isValidPassworddelivery) {
            access_token = jwt.sign(
                {
                    driverId: driver.id,
                    name: driver.name,
                    type: 'driver'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );

           /* if (driver.currentToken && driver.currentToken !== access_token) {
                return res.status(403).json({
                    success: false,
                    message: 'مستخدم مسجل دخول من جهاز آخر',
                    data: null
                });
            }*/

            driver.currentToken = access_token;
            await driver.save();
        }


        res.json({
            success: true,
            message: 'تم تسجيل الدخول بنجاح',
            data: {
                access_token,
                type: customer ? 'customer' : 'driver'
            }
        });

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
}


    /**
     * تسجيل عميل جديد
     * POST /api/customers/register
     */
    async register(req, res) {
        try {
            const { name, email, phone, password, address, areaId } = req.body;

            // التحقق من البيانات المطلوبة
            if (!name || !email || !phone || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'جميع البيانات الأساسية مطلوبة',
                    data: null
                });
            }

            // التحقق من عدم وجود العميل مسبقاً
            const existingCustomer = await Customer.findOne({
                where: {
                    [Op.or]: [
                        { email: email },
                        { phone: phone }
                    ]
                }
            });

            if (existingCustomer) {
                return res.status(409).json({
                    success: false,
                    message: 'البريد الإلكتروني أو رقم الهاتف مستخدم مسبقاً',
                    data: null
                });
            }

            // تشفير كلمة المرور
            const hashedPassword = await bcrypt.hash(password, 10);

            // إنشاء العميل الجديد
            const customer = await Customer.create({
                name,
                email,
                phone,
                password: hashedPassword,
                address,
                areaId
            });

            // إنشاء JWT token
            const token = jwt.sign(
                { 
                    customerId: customer.id,
                    email: customer.email,
                    type: 'customer'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );

            res.status(201).json({
                success: true,
                message: 'تم إنشاء الحساب بنجاح',
                data: {
                    token,
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        email: customer.email,
                        phone: customer.phone,
                        address: customer.address,
                        areaId: customer.areaId,
                        createdAt: customer.createdAt
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تسجيل العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على بيانات العميل
     * GET /api/customers/profile
     */
    async getProfile(req, res) {
        try {
            const customerId = req.customer.id;

            const customer = await Customer.findByPk(customerId, {
                attributes: ['id', 'name', 'barcode', 'image', 'phoneNumber', 'address', 'city', 'region', 'notes']
            });

            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            const ordercount = await Order.count({
                where: {
                    customerId: customer.id
                }
            });

            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: {
                    id: customer.id,
                    name: customer.name,
                    barcode: customer.barcode,
                    image: customer.image,
                    phoneNumber: customer.phoneNumber,
                    address: customer.address,
                    city: customer.city,
                    region: customer.region,
                    notes: customer.notes,
                    orderCount: ordercount
                }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تحديث بيانات العميل
     * PUT /api/customers/profile
     */



    async getCityAndRegion(lat, lon) {
        try {
            const geocoder = NodeGeocoder({
            provider: 'openstreetmap'
            });
            const res = await geocoder.reverse({ lat, lon });

            if (res.length === 0) return { city: '', region: '' };

            const location = res[0];

            const city =
                location.city ||
                location.town ||
                location.village ||
                location.hamlet ||
                '';

            const region =
                location.state ||
                location.region ||
                location.county ||
                '';

            return { city, region };
        } catch (error) {
            console.error('فشل في جلب المدينة والمنطقة:', error.message);
            return { city: '', region: '' };
        }
    }

    async updateProfile(req, res) {
        try {
            const customerId = req.customer.id;
            let {
                name,
                barcode,
                phoneNumber,
                address,
                city,
                region,
                latitude,
                longitude,
                notes,
                discountRate,
                status
            } = req.body;

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // إذا تم رفع صورة، نحفظ اسم الملف
            let imagePath = customer.image;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            // إذا لم يتم إرسال city و region ولكن تم إرسال latitude و longitude
            if (latitude && longitude) {
                const location = await this.getCityAndRegion(latitude, longitude);
                address = address || location.address;
                city = city || location.city;
                region = region || location.region;
            }

           await customer.update({
                name: name ?? customer.name,
                barcode: barcode ?? customer.barcode,
                phoneNumber: phoneNumber ?? customer.phoneNumber,
                image: imagePath,
                address: address ?? customer.address,
                city: city ?? customer.city,
                region: region ?? customer.region,
                latitude: latitude ?? customer.latitude,
                longitude: longitude ?? customer.longitude,
                notes: notes ?? customer.notes,
                discountRate: discountRate ?? customer.discountRate,
                status: status ?? customer.status,
            });

            res.json({
                success: true,
                message: 'تم تحديث البيانات بنجاح',
                data: {
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        phoneNumber: customer.phoneNumber,
                        image: customer.image,
                        address: customer.address,
                        city: city,
                        region: region,
                        latitude: customer.latitude,
                        longitude: customer.longitude,
                        notes: customer.notes,
                        discountRate: customer.discountRate,
                        status: customer.status,
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تغيير كلمة المرور
     * PUT /api/customers/change-password
     */
    async changePassword(req, res) {
        try {
            const customerId = req.customer.id;
            const { currentPassword, newPassword } = req.body;

            if (!currentPassword || !newPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'كلمة المرور الحالية والجديدة مطلوبتان',
                    data: null
                });
            }

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // التحقق من كلمة المرور الحالية
            const isValidPassword = await bcrypt.compare(currentPassword, customer.password);
            if (!isValidPassword) {
                return res.status(401).json({
                    success: false,
                    message: 'كلمة المرور الحالية غير صحيحة',
                    data: null
                });
            }

            // تشفير كلمة المرور الجديدة
            const hashedPassword = await bcrypt.hash(newPassword, 10);

            // تحديث كلمة المرور
            await customer.update({ password: hashedPassword });

            res.json({
                success: true,
                message: 'تم تغيير كلمة المرور بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في تغيير كلمة المرور:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

     async getProfileDriver(req, res) {
        try {
            const driverId = req.driver.id;

            const driver = await DeliveryPerson.findByPk(driverId, {
                attributes: { exclude: ['password'] }
            });

            if (!driver) {
                return res.status(404).json({
                    success: false,
                    message: 'السائق غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: { driver }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات السائق:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async changePasswordDriver(req, res) {
        try {
            const driverId = req.driver.id;
            const { currentPassword, newPassword } = req.body;

            if (!currentPassword || !newPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'كلمة المرور الحالية والجديدة مطلوبتان',
                    data: null
                });
            }

            const driver = await DeliveryPerson.findByPk(driverId);
            if (!driver) {
                return res.status(404).json({
                    success: false,
                    message: 'السائق  غير موجود',
                    data: null
                });
            }

            // التحقق من كلمة المرور الحالية
            const isValidPassword = await bcrypt.compare(currentPassword, driver.password);
            if (!isValidPassword) {
                return res.status(401).json({
                    success: false,
                    message: 'كلمة المرور الحالية غير صحيحة',
                    data: null
                });
            }

            // تشفير كلمة المرور الجديدة
            const hashedPassword = await bcrypt.hash(newPassword, 10);

            // تحديث كلمة المرور
            await driver.update({ password: hashedPassword });

            res.json({
                success: true,
                message: 'تم تغيير كلمة المرور بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في تغيير كلمة المرور:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تسجيل الخروج
     * POST /api/customers/logout
     */
    async logout(req, res) {
        try {
            const authHeader = req.headers.authorization;
            if (!authHeader) return res.status(400).send("مطلوب التوكن");

            const token = authHeader.split(' ')[1];
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

            if (decoded.type === 'customer') {
                const customer = await Customer.findByPk(decoded.customerId);
                if (customer) {
                    customer.currentToken = null;
                    await customer.save();
                }
            } else if (decoded.type === 'driver') {
                const driver = await DeliveryPerson.findByPk(decoded.driverId);
                if (driver) {
                    driver.currentToken = null;
                    await driver.save();
                }
            }

            res.json({ message: 'تم تسجيل الخروج بنجاح' });
        } catch (error) {
            res.status(500).json({ message: 'خطأ في تسجيل الخروج' });
        }
    }



    async saveFirebaseToken(req, res) {
        try {
            const { token, deviceInfo } = req.body;
            const customerId = req.customer.id;
            console.log(token);
            console.log(customerId);
            if (!token) {
                return res.status(400).json({
                    success: false,
                    message: 'توكن Firebase مطلوب',
                    data: null
                });
            }

            // البحث عن التوكن الموجود
            const existingToken = await CustomerToken.findOne({
                where: { token }
            });

            if (existingToken) {
                // تحديث التوكن الموجود
                await existingToken.update({
                    customerId,
                    deviceInfo,
                    isActive: true,
                    lastUsed: new Date()
                });
            } else {
                // إنشاء توكن جديد
                await CustomerToken.create({
                    token,
                    customerId,
                    deviceInfo,
                    isActive: true,
                    lastUsed: new Date()
                });
            }

            res.json({
                success: true,
                message: 'تم حفظ توكن الإشعارات بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في حفظ توكن Firebase:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async saveTokendriver(req, res) {
        try {
            const { token, deviceInfo } = req.body;
            const driverId = req.driver.id;
            console.log(token);
            console.log(driverId);
            if (!token) {
                return res.status(400).json({
                    success: false,
                    message: 'توكن Firebase مطلوب',
                    data: null
                });
            }

            // البحث عن التوكن الموجود
            const existingToken = await DriverToken.findOne({
                where: { token }
            });

            if (existingToken) {
                // تحديث التوكن الموجود
                await existingToken.update({
                    driverId,
                    deviceInfo,
                    isActive: true,
                    lastUsed: new Date()
                });
            } else {
                // إنشاء توكن جديد
                await DriverToken.create({
                    token,
                    driverId,
                    deviceInfo,
                    isActive: true,
                    lastUsed: new Date()
                });
            }

            res.json({
                success: true,
                message: 'تم حفظ توكن الإشعارات بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في حفظ توكن Firebase:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
    
}

module.exports = new CustomerApiController();

<!-- Main Content -->
<div class="container-main">
    <div class="form-card">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 style="color: var(--primary-color);">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة منتج جديد
                </h2>
                <p class="text-muted mb-0">أضف منتج جديد إلى المتجر</p>
            </div>
            <a href="/admin/products" class="btn btn-secondary-custom">
                <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
            </a>
        </div>

        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-custom mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <%= error %>
            </div>
        <% } %>

        <form action="/admin/products" method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag me-2"></i>اسم المنتج *
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<%= typeof formData !== 'undefined' ? formData.name || '' : '' %>" 
                               required placeholder="أدخل اسم المنتج">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>وصف المنتج
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="أدخل وصف المنتج"><%= typeof formData !== 'undefined' ? formData.description || '' : '' %></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="barcode" class="form-label">
                            <i class="fas fa-barcode me-2"></i>الباركود (اختياري)
                        </label>
                        <input type="text" class="form-control" id="barcode" name="barcode"
                               value="<%= typeof formData !== 'undefined' ? formData.barcode || '' : '' %>"
                               placeholder="أدخل رقم الباركود">
                        <small class="form-text text-muted">اتركه فارغاً إذا لم يكن متوفراً</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">
                                    <i class="fas fa-money-bill me-2"></i>السعر (ل.س) *
                                </label>
                                <input type="number" class="form-control" id="price" name="price"
                                       value="<%= typeof formData !== 'undefined' ? formData.price || '' : '' %>"
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-boxes me-2"></i>الكمية المتاحة *
                                </label>
                                <input type="number" class="form-control" id="quantity" name="quantity"
                                       value="<%= typeof formData !== 'undefined' ? formData.quantity || '' : '' %>"
                                       min="0" required placeholder="0">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="categoryId" class="form-label">
                            <i class="fas fa-list me-2"></i>الفئة *
                        </label>
                        <select class="form-select" id="categoryId" name="categoryId" required>
                            <option value="">اختر الفئة</option>
                            <% if (typeof categories !== 'undefined' && categories) { %>
                                <% categories.forEach(category => { %>
                                    <option value="<%= category.id %>" 
                                            <%= typeof formData !== 'undefined' && formData.categoryId == category.id ? 'selected' : '' %>>
                                        <%= category.name %>
                                    </option>
                                <% }); %>
                            <% } %>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="images" class="form-label">
                            <i class="fas fa-images me-2"></i>صور المنتج
                        </label>
                        <div class="file-upload-area file-upload-wrapper border rounded p-3 text-center"
                             style="background-color: rgba(178, 205, 156, 0.1); cursor: pointer;">
                            <!-- input مخفي -->
                            <input type="file" class="form-control" id="images" name="images"
                                   multiple accept="image/*" style="display: none;">
                            <div class="upload-instructions">
                                <i class="fas fa-cloud-upload-alt fa-2x mb-2" style="color: var(--primary-color);"></i>
                                <p>اسحب وأفلت الصور هنا أو انقر للاختيار</p>
                                <small class="form-text text-muted">يمكنك اختيار عدة صور (حد أقصى 10 صور، 5MB لكل صورة)</small>
                            </div>
                        </div>
                        <div id="preview-container" class="mt-3" style="display: none;">
                            <h6>الصور المختارة:</h6>
                            <div id="image-previews" class="d-flex flex-wrap gap-2"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-3 mt-4">
                <a href="/admin/products" class="btn btn-secondary-custom">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
                <button type="submit" class="btn btn-custom">
                    <i class="fas fa-save me-2"></i>حفظ المنتج
                </button>
            </div>
        </form>
    </div>
</div>


<script>
    const uploadArea = document.querySelector('.file-upload-area');
    const imagesInput = document.getElementById('images');
    const previewContainer = document.getElementById('preview-container');
    const imagePreviews = document.getElementById('image-previews');

    uploadArea.addEventListener('click', (e) => {
        // إذا الضغط داخل صور المعاينة (داخل div#image-previews) لا تفتح نافذة اختيار الملفات
        if (e.target.closest('#image-previews')) {
            e.stopPropagation();
            return;
        }
        // افتح نافذة اختيار الصور عند الضغط في المنطقة (باستثناء الصور)
        imagesInput.click();
    });

    imagesInput.addEventListener('change', () => {
        const files = imagesInput.files;
        imagePreviews.innerHTML = '';

        if (files.length > 0) {
            previewContainer.style.display = 'block';

            Array.from(files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'position-relative me-2 mb-2';
                        imageDiv.innerHTML = `
                            <img src="${e.target.result}" alt="معاينة ${index + 1}"
                                style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; border: 2px solid var(--primary-color);">
                            <small class="d-block text-center mt-1">${file.name}</small>
                        `;
                        imagePreviews.appendChild(imageDiv);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            previewContainer.style.display = 'none';
        }
    });
</script>

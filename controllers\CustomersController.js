const BaseController = require('./BaseController');
const { Customer, Order, Category, Product, Image, OrderDetail, Notification,Delivery,DeliveryPerson,CustomerToken,DriverToken, sequelize ,Offer} = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const NotificationsFunction = require('../controllers/NotificationsFunction');
const { Op, where } = require('sequelize');
const bcrypt = require('bcryptjs');
const { custom } = require('joi');


class CustomersController extends BaseController {
    constructor() {
        super(Customer, 'customers');
    }
 
     async getHome(req, res) {
        try {
            const customerId = req.customer?.id;

            // المنتجات التي تحتوي على صورة (offers)
            const offers = await Offer.findAll({
               attributes: [
                            'id', 'productId', 'discount_value', 'price_after_discount',
                            'image', 'start_date', 'end_date'
                        ],
                order: [['start_date', 'DESC']]
            });

            // المنتجات الأعلى تقييماً
            const topProducts = await Product.findAll({
            where: {
                rating: {
                [Op.not]: null
                },
                status: 'active'
            },
            include: [
                {
                model: Image,
                as: 'images',
                attributes: ['image'],
                required: false
                }
            ],
            attributes: ['id', 'name', 'description', 'price', 'quantity', 'rating'],
            order: [['rating', 'DESC']],
            limit: 10
            });

            // الاستجابة
            res.json({
            success: true,
            message: 'تم جلب البيانات بنجاح',
            data: {
                offers,
                topProducts
            }
            });

        } catch (error) {
            console.error('خطأ في جلب الصفحة الرئيسية:', error);
            res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
            });
        }
    }

    // عرض قائمة العملاء للإدارة
    async index(req, res) {
        try {
            // إعداد خيارات البحث والفلتر
            const searchFields = {
                text: ['name', 'barcode', 'phoneNumber'],
                numeric: ['id']
            };

            const filterFields = {
                status: { type: 'exact' },
                createdAt: { type: 'date' }
            };
            const { searchConditions, filterConditions } = buildSearchAndFilter(
                req.query,
                searchFields,
                filterFields
            );

            // دمج الشرطين مع التحقق من عدم تمرير {} فارغ
            const whereClause = {
                ...(Object.keys(searchConditions).length > 0 ? { [Op.and]: [searchConditions] } : {}),
                ...(Object.keys(filterConditions).length > 0 ? filterConditions : {})
            };

            // خيارات الترتيب
            const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

            // خيارات الـ pagination
            const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

            // جلب البيانات
            const { count, rows: customers } = await Customer.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Order, as: 'orders' }
                ],
                order: sortOptions,
                limit: paginationOptions.limit,
                offset: paginationOptions.offset,
                distinct: true
            });

            // حساب معلومات الـ pagination
            const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

            // تنظيف الفلاتر للعرض
            const filters = sanitizeFilters(req.query);

            res.render('admin/customers/index', {
                customers,
                pagination,
                filters,
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: Object.keys(filters).length
            });
        } catch (error) {
            console.error('Error fetching customers:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch customers' } });
        }
    }

    // عرض تفاصيل عميل
    async show(req, res) {
        const customerId = req.params.id;

        try {
           const customer = await Customer.findByPk(customerId, {
                include: [
                    {
                        model: Order,
                        as: 'orders',
                        separate: true, // ضروري عند استخدام order داخل include
                        order: [['createdAt', 'DESC']]
                    }
                ]
            });

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            res.render('admin/customers/show', { customer });
        } catch (error) {
            console.error("Error fetching customer:", error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب بيانات العميل' } });
        }
    }

    // عرض نموذج إضافة عميل جديد
    async create(req, res) {
        try {
            res.render('admin/customers/create', {
                title: 'إضافة عميل جديد'
            });
        } catch (error) {
            console.error('Error loading create customer form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // إنشاء عميل جديد
    async insert(req, res) {
      try {
       const { name, phoneNumber, password, barcode, discountRate, notes, status, address, latitude, longitude, city, region, permissions } = req.body;
    
         // التحقق من البيانات المطلوبة
            if (!name || !phoneNumber || !password) {
                return res.status(400).render('admin/customers/create', {
                    error: 'الاسم ورقم الهاتف وكلمة المرور مطلوبة',
                    formData: req.body
                });
            }

            // التحقق من عدم وجود عميل بنفس رقم الهاتف
            const existingCustomer = await Customer.findOne({ where: { phoneNumber } });
            const existingDriver = await DeliveryPerson.findOne({ where: { phoneNumber } });
            if (existingCustomer || existingDriver) {
                return res.status(400).render('admin/customers/create', {
                    error: 'يوجد عميل مسجل بهذا رقم الهاتف',
                    formData: req.body
                });
            }

            const existingName = await Customer.findOne({ where: { name } });
            const existingNameDriver = await DeliveryPerson.findOne({ where: { username: name } });
            if (existingName || existingNameDriver) {
                return res.status(400).render('admin/customers/create', {
                    error: 'اسم العميل مستخدم بالفعل',
                    formData: req.body
                });
            }
        
        // تشفير كلمة المرور
          // معالجة رفع الصورة
            let imagePath = null;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            // معالجة الصلاحيات (0 أو 1)
            const customerPermissions = parseInt(permissions) || 0;
        // إنشاء العميل
            const newCustomer = await Customer.create({
                name,
                phoneNumber,
                password: password,
                barcode: barcode || null,
                image: imagePath,
                discountRate: parseFloat(discountRate) || 0,
                notes: notes || null,
                address: address || null,
                city: city || null,
                region: region || null,
                latitude: latitude ? parseFloat(latitude) : null,
                longitude: longitude ? parseFloat(longitude) : null,
                permissions: customerPermissions,
                status: status || 'active'
            });

            // إرسال إشعار للمدراء عن العميل الجديد
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                const notification = {
                    title: 'عميل جديد',
                    body: `تم تسجيل عميل جديد: ${name} - ${phoneNumber}`
                };

                const data = {
                    customerId: newCustomer.id.toString(),
                    customerName: name,
                    customerPhone: phoneNumber,
                    clickAction: `/admin/customers/${newCustomer.id}`,
                    type: 'new_customer'
                };

                const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
            } catch (notificationError) {
                console.error('❌ Error sending customer notification:', notificationError);
                // لا نوقف العملية إذا فشل الإشعار
            }
    
            req.flash('success', 'تم إضافة العميل بنجاح');
            res.redirect('/admin/customers');
      } catch (error) {
            console.error('Error creating customer:', error);
            res.status(500).render('admin/customers/create', {
                error: 'حدث خطأ أثناء إضافة العميل',
                formData: req.body
            });
        }
    };
    // عرض نموذج تعديل عميل
    async edit(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            res.render('admin/customers/edit', {
                customer,
                title: `تعديل العميل: ${customer.name}`
            });
        } catch (error) {
            console.error('Error loading edit customer form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // تحديث بيانات عميل
    async update(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            const { name, phoneNumber, barcode, discountRate, notes, status, password, address, latitude, longitude, city, region, permissions } = req.body;

           const existingCustomer = await Customer.findOne({ where: { phoneNumber, id: { [Op.ne]: customer.id } } });
            const existingPhone = await DeliveryPerson.findOne({
            where: {
                phoneNumber
            }
            });
            if (existingCustomer || existingPhone) {
            return res.status(400).send('رقم الهاتف مستخدم بالفعل لعميل آخر');
            }


            const existingName = await Customer.findOne({ where: { name ,id: { [Op.ne]: customer.id } } });
            const existingNameDriver = await DeliveryPerson.findOne({ where: { username : name } });
            if (existingName || existingNameDriver) {
            console.log('اسم العميل مستخدم بالفعل');
                return res.status(400).render('admin/drivers/create', {
                    error: 'اسم العميل مستخدم بالفعل',
                    formData: req.body
                });
            }

            // معالجة رفع الصورة الجديدة
            let imagePath = customer.image;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            // معالجة الصلاحيات (0 أو 1)
            const customerPermissions = parseInt(permissions) || 0;

            // إعداد البيانات للتحديث
            const updateData = {
                name,
                phoneNumber,
                barcode: barcode || null,
                image: imagePath,
                discountRate: parseFloat(discountRate) || 0,
                notes: notes || null,
                address: address || null,
                city: city || null,
                region: region || null,
                latitude: latitude ? parseFloat(latitude) : null,
                longitude: longitude ? parseFloat(longitude) : null,
                permissions: customerPermissions,
                status
            };

            // تحديث كلمة المرور إذا تم إدخالها
            if (password && password.trim() !== '') {
                updateData.password = password;
            }

            // تحديث البيانات
            await customer.update(updateData);

            req.flash('success', 'تم تحديث بيانات العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer:', error);
            res.status(500).render('admin/customers/edit', {
                error: 'حدث خطأ أثناء تحديث البيانات',
                customer: { ...req.body, id: req.params.id }
            });
        }
    }

    // حذف عميل
    async delete(req, res) {
        try {
            console.log('Deleting customer:', req.params.id);
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            // حذف ناعم - تغيير الحالة إلى inactive
            await customer.update({ status: 'inactive' });

            req.flash('success', 'تم حذف العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error deleting customer:', error);
            req.flash('error', 'حدث خطأ أثناء حذف العميل');
            res.redirect('/admin/customers');
        }
    }

    // تحديث حالة عميل
    async updateStatus(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            const { status } = req.body;

            if (!['active', 'inactive', 'pending'].includes(status)) {
                req.flash('error', 'حالة غير صحيحة');
                return res.redirect('/admin/customers');
            }

            const oldStatus = customer.status;
            await customer.update({ status });

            // إرسال إشعار للمدراء عن تغيير حالة العميل
            if (oldStatus !== status) {
                try {
                    const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                    const statusText = {
                        'active': 'نشط',
                        'inactive': 'غير نشط',
                        'pending': 'في الانتظار'
                    };

                    const notification = {
                        title: 'تحديث حالة عميل',
                        body: `تم تغيير حالة العميل ${customer.name} من ${statusText[oldStatus]} إلى ${statusText[status]}`
                    };

                    const data = {
                        customerId: customer.id.toString(),
                        customerName: customer.name,
                        oldStatus: oldStatus,
                        newStatus: status,
                        clickAction: `/admin/customers/${customer.id}`,
                        type: 'customer_status_update'
                    };

                    const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
                } catch (notificationError) {
                    console.error('❌ Error sending customer status notification:', notificationError);
                }
            }

            req.flash('success', `تم تحديث حالة العميل إلى ${status}`);
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer status:', error);
            req.flash('error', 'حدث خطأ أثناء تحديث الحالة');
            res.redirect('/admin/customers');
        }
    }

}

module.exports = new CustomersController();
 
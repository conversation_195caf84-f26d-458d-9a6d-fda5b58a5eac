
    <style>


        .container-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .product-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border-left: 5px solid var(--primary-color);
        }

        .product-image {
            border-radius: 15px;
            box-shadow: var(--shadow);
            max-width: 100%;
            height: auto;
        }

        .product-gallery {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .gallery-thumb {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            object-fit: cover;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .gallery-thumb:hover,
        .gallery-thumb.active {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .info-item {
            background: rgba(178, 205, 156, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
        }

        .info-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .info-value {
            color: var(--text-light);
            font-size: 1.1rem;
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

        .btn-secondary-custom {
            background: #6c757d;
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary-custom:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: var(--white);
        }

        .btn-warning-custom {
            background: #f39c12;
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-warning-custom:hover {
            background: #e67e22;
            transform: translateY(-2px);
            color: var(--white);
        }

        .price-badge {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }
            
            .product-card {
                padding: 1.5rem;
            }
        }
    </style>


    <!-- Main Content -->
    <div class="container-main">
        <div class="product-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: var(--primary-color);">
                        <i class="fas fa-box me-2"></i>
                        تفاصيل المنتج
                    </h2>
                    <p class="text-muted mb-0">عرض تفاصيل المنتج الكاملة</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/admin/products/<%= product.id %>/edit" class="btn btn-warning-custom">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                    <a href="/admin/products" class="btn btn-secondary-custom">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <% if (product.images && product.images.length > 0) { %>
                        <img src="<%= product.images[0].image %>" alt="<%= product.name %>" 
                             class="product-image w-100" id="mainImage">
                        
                        <% if (product.images.length > 1) { %>
                            <div class="product-gallery">
                                <% product.images.forEach((image, index) => { %>
                                    <img src="<%= image.image %>" alt="<%= product.name %>" 
                                         class="gallery-thumb <%= index === 0 ? 'active' : '' %>"
                                         onclick="changeMainImage('<%= image.image %>', this)">
                                <% }); %>
                            </div>
                        <% } %>
                    <% } else { %>
                        <div class="text-center p-5" style="background: rgba(178, 205, 156, 0.1); border-radius: 15px;">
                            <i class="fas fa-image fa-3x mb-3" style="color: var(--primary-color);"></i>
                            <p class="text-muted">لا توجد صور للمنتج</p>
                        </div>
                    <% } %>
                </div>

                <div class="col-md-6">
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-tag me-2"></i>اسم المنتج
                        </div>
                        <div class="info-value"><%= product.name %></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-money-bill me-2"></i>السعر
                        </div>
                        <div class="info-value">
                            <span class="price-badge"><%= product.price %> ل.س</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-list me-2"></i>الفئة
                        </div>
                        <div class="info-value">
                            <% if (product.category) { %>
                                <span class="badge bg-primary"><%= product.category.name %></span>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-boxes me-2"></i>الكمية المتاحة
                        </div>
                        <div class="info-value">
                            <span class="badge bg-info"><%= product.quantity %> قطعة</span>
                        </div>
                    </div>

                    <% if (product.barcode) { %>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-barcode me-2"></i>الباركود
                            </div>
                            <div class="info-value">
                                <code style="background: #f8f9fa; padding: 0.25rem 0.5rem; border-radius: 4px;"><%= product.barcode %></code>
                            </div>
                        </div>
                    <% } %>

                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-info-circle me-2"></i>الحالة
                        </div>
                        <div class="info-value">
                            <span class="status-badge <%= product.status === 'active' ? 'status-active' : 'status-inactive' %>">
                                <%= product.status === 'active' ? 'نشط' : 'غير نشط' %>
                            </span>
                        </div>
                    </div>

                    <% if (product.description) { %>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-align-left me-2"></i>الوصف
                            </div>
                            <div class="info-value"><%= product.description %></div>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <script>
        function changeMainImage(imageSrc, thumbElement) {
            document.getElementById('mainImage').src = imageSrc;
            
            // Remove active class from all thumbnails
            document.querySelectorAll('.gallery-thumb').forEach(thumb => {
                thumb.classList.remove('active');
            });
            
            // Add active class to clicked thumbnail
            thumbElement.classList.add('active');
        }
    </script>
